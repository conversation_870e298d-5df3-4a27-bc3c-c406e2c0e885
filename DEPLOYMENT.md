# Deployment Guide

## Prerequisites

- Node.js 18+ installed
- npm or yarn package manager
- Modern web browser with push notification support

## Environment Setup

### Server Environment Variables

Create a `.env` file in the `server` directory with the following variables:

```env
# VAPID Keys for Push Notifications (generate with: npx web-push generate-vapid-keys)
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# Server Configuration
PORT=3001
```

### Generating VAPID Keys

Run the following command in the server directory to generate VAPID keys:

```bash
cd server
npx web-push generate-vapid-keys
```

Copy the generated keys to your `.env` file.

## Local Development

1. **Install all dependencies:**
   ```bash
   npm run install:all
   ```

2. **Start both server and client:**
   ```bash
   npm run dev
   ```

   Or start them separately:
   ```bash
   # Terminal 1 - Start server
   npm run server:dev
   
   # Terminal 2 - Start client
   npm run client:dev
   ```

3. **Access the application:**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001

## Production Deployment

### Server Deployment

1. **Build and start the server:**
   ```bash
   cd server
   npm install --production
   npm start
   ```

2. **Environment variables for production:**
   - Set `PORT` to your desired port (default: 3001)
   - Update CORS origins in `server/index.js` to match your frontend domain
   - Set proper VAPID keys for push notifications

### Client Deployment

1. **Build the client:**
   ```bash
   cd client
   npm run build
   ```

2. **Serve the built files:**
   The `dist` folder contains the built application. Serve it using:
   - Static hosting (Netlify, Vercel, GitHub Pages)
   - Web server (Nginx, Apache)
   - CDN

3. **Update API endpoints:**
   - Update the server URL in `client/src/hooks/useSocket.ts`
   - Update the VAPID public key endpoint in `client/src/utils/pushNotifications.ts`

### Docker Deployment (Optional)

Create `Dockerfile` for the server:

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY server/package*.json ./
RUN npm install --production
COPY server/ .
EXPOSE 3001
CMD ["npm", "start"]
```

Create `docker-compose.yml`:

```yaml
version: '3.8'
services:
  chat-server:
    build: .
    ports:
      - "3001:3001"
    environment:
      - VAPID_PUBLIC_KEY=${VAPID_PUBLIC_KEY}
      - VAPID_PRIVATE_KEY=${VAPID_PRIVATE_KEY}
      - PORT=3001
```

## Security Considerations

1. **HTTPS Required:** Push notifications require HTTPS in production
2. **CORS Configuration:** Update CORS settings for your domain
3. **Environment Variables:** Never commit `.env` files to version control
4. **Rate Limiting:** Consider implementing rate limiting for production
5. **Input Validation:** Add proper input validation and sanitization

## Monitoring and Logging

- Monitor server logs for connection issues
- Track push notification delivery rates
- Monitor WebSocket connection stability
- Set up error tracking (Sentry, LogRocket, etc.)

## Troubleshooting

### Common Issues

1. **Push notifications not working:**
   - Check VAPID keys are correctly set
   - Ensure HTTPS is used in production
   - Verify browser permissions

2. **WebSocket connection fails:**
   - Check CORS configuration
   - Verify server is running and accessible
   - Check firewall settings

3. **Build errors:**
   - Clear node_modules and reinstall dependencies
   - Check Node.js version compatibility
   - Verify all environment variables are set
