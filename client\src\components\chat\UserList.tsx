import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Users, Circle } from 'lucide-react';
import { User } from '@/types/chat';

interface UserListProps {
  users: User[];
  currentUserId?: string;
  typingUsers: string[];
}

export const UserList: React.FC<UserListProps> = ({ users, currentUserId, typingUsers }) => {
  return (
    <Card className="w-64 h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Users className="h-4 w-4" />
          Online Users ({users.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-8rem)]">
          <div className="space-y-1 p-3">
            {users.map((user) => {
              const isCurrentUser = user.socketId === currentUserId;
              const isTyping = typingUsers.includes(user.username);
              
              return (
                <div
                  key={user.socketId}
                  className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50 transition-colors"
                >
                  <Circle 
                    className="h-2 w-2 fill-green-500 text-green-500" 
                  />
                  <span className="text-sm flex-1">
                    {user.username}
                    {isCurrentUser && (
                      <span className="text-muted-foreground ml-1">(You)</span>
                    )}
                  </span>
                  {isTyping && (
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce"></div>
                      <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-1 h-1 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  )}
                </div>
              );
            })}
            {users.length === 0 && (
              <div className="text-center text-muted-foreground text-sm py-4">
                No users online
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
