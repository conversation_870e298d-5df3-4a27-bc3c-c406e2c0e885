import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { MessageList, Message } from './MessageList';
import { MessageInput } from './MessageInput';
import { UserList, User } from './UserList';
import { TypingIndicator } from './TypingIndicator';
import { MessageCircle } from 'lucide-react';

interface ChatLayoutProps {
  messages: Message[];
  users: User[];
  currentUserId?: string;
  typingUsers: string[];
  onSendMessage: (message: string) => void;
  onTyping: (isTyping: boolean) => void;
  isConnected: boolean;
}

export const ChatLayout: React.FC<ChatLayoutProps> = ({
  messages,
  users,
  currentUserId,
  typingUsers,
  onSendMessage,
  onTyping,
  isConnected
}) => {
  return (
    <div className="flex h-screen bg-background">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        <Card className="flex-1 rounded-none border-0 border-r">
          <CardHeader className="border-b">
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Chat Room
              <div className="flex items-center gap-2 ml-auto">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                <span className="text-sm font-normal text-muted-foreground">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0 flex flex-col h-full">
            <MessageList messages={messages} currentUserId={currentUserId} />
            <TypingIndicator typingUsers={typingUsers} />
            <MessageInput 
              onSendMessage={onSendMessage} 
              onTyping={onTyping}
              disabled={!isConnected}
            />
          </CardContent>
        </Card>
      </div>

      {/* User List Sidebar */}
      <div className="hidden md:block">
        <UserList 
          users={users} 
          currentUserId={currentUserId}
          typingUsers={typingUsers}
        />
      </div>
    </div>
  );
};
